#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AugmentCode 终极彻底清理工具 - 修复版
用于清理各种IDE的缓存、配置和临时文件
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import os
import sys
import shutil
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime

# 导入增强功能的第三方库
try:
    import psutil
    import send2trash
    from tqdm import tqdm
    import colorama
    import regex as re
    from dateutil import parser as date_parser
    ENHANCED_MODE = True
    print("✅ 增强模式已启用")
except ImportError as e:
    print(f"⚠️ 部分增强功能不可用: {e}")
    print("💡 运行 install_dependencies.bat 安装完整依赖")
    ENHANCED_MODE = False
    import re

# Windows特定导入
try:
    import winreg
    import wmi
    WINDOWS_ENHANCED = True
except ImportError:
    WINDOWS_ENHANCED = False
    winreg = None

# 初始化colorama（如果可用）
if ENHANCED_MODE:
    colorama.init()

class AugmentCodeCleaner:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.detect_ides()
        
    def setup_window(self):
        """设置主窗口"""
        self.root.title("AugmentCode 终极彻底清理工具 - 修复版")
        self.root.geometry("1200x800")
        self.root.resizable(False, False)

        # 设置窗口背景色为蓝色主题（与原图一致）
        self.root.configure(bg='#4472C4')

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # 设置窗口居中
        self.center_window()


        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_variables(self):
        """设置变量"""
        self.ide_vars = {
            'vscode': tk.BooleanVar(),
            'cursor': tk.BooleanVar(), 
            'idea': tk.BooleanVar(),
            'pycharm': tk.BooleanVar()
        }
        
        self.ide_info = {
            'vscode': {'name': 'VS Code', 'icon': '💻', 'detected': False, 'size': '未检测'},
            'cursor': {'name': 'Cursor', 'icon': '🎯', 'detected': False, 'size': '未检测'},
            'idea': {'name': 'IDEA', 'icon': '🧠', 'detected': False, 'size': '未检测'},
            'pycharm': {'name': 'PyCharm', 'icon': '🐍', 'detected': False, 'size': '未检测'}
        }
        
        self.is_scanning = False
        self.is_cleaning = False
        
    def create_widgets(self):
        """创建界面组件 - 完全按照原图设计"""
        # 顶部标题栏 - 蓝色背景，白色文字
        title_frame = tk.Frame(self.root, bg='#4472C4', height=80)
        title_frame.pack(fill='x', padx=0, pady=0)
        title_frame.pack_propagate(False)

        # 火箭图标和标题
        title_label = tk.Label(title_frame, text="🚀 AugmentCode 终极彻底清理工具 - 修复版",
                              font=("Microsoft YaHei", 18, "bold"),
                              bg='#4472C4', fg='white')
        title_label.pack(expand=True)

        # 主内容区域 - 浅蓝色背景
        main_frame = tk.Frame(self.root, bg='#E7F3FF')
        main_frame.pack(fill='both', expand=True, padx=0, pady=0)

        # 警告提示栏 - 黄色背景
        warning_frame = tk.Frame(main_frame, bg='#FFF2CC', height=50)
        warning_frame.pack(fill='x', padx=15, pady=15)
        warning_frame.pack_propagate(False)

        warning_icon = tk.Label(warning_frame, text="⚠️", font=("Segoe UI Emoji", 16),
                               bg='#FFF2CC', fg='#D6B656')
        warning_icon.pack(side='left', padx=(15, 10), pady=15)

        warning_text = "注意：此工具将彻底清理选中IDE的所有缓存、配置和临时文件，请确保已关闭所有IDE！"
        warning_label = tk.Label(warning_frame, text=warning_text,
                                font=("Microsoft YaHei", 10),
                                bg='#FFF2CC', fg='#8B4513', justify='left')
        warning_label.pack(side='left', pady=15)
        
        # IDE选择区域标题
        ide_title_frame = tk.Frame(main_frame, bg='#E7F3FF')
        ide_title_frame.pack(fill='x', padx=15, pady=(0, 10))

        ide_title_label = tk.Label(ide_title_frame, text="选择要终极清理的IDE",
                                  font=("Microsoft YaHei", 12, "bold"),
                                  bg='#E7F3FF', fg='#4472C4')
        ide_title_label.pack(anchor='w')

        # IDE选择区域
        ide_frame = tk.Frame(main_frame, bg='#E7F3FF')
        ide_frame.pack(fill='x', padx=15, pady=(0, 15))

        self.create_ide_selection(ide_frame)

        # 操作按钮区域
        button_frame = tk.Frame(main_frame, bg='#E7F3FF')
        button_frame.pack(fill='x', padx=15, pady=(0, 15))

        # 全部深度扫描按钮 - 蓝色
        self.scan_button = tk.Button(button_frame, text="🔍 全部深度扫描",
                                    command=self.start_scan,
                                    font=("Microsoft YaHei", 11, "bold"),
                                    bg='#4472C4', fg='white',
                                    width=18, height=2,
                                    relief='flat', cursor='hand2')
        self.scan_button.pack(side='left', padx=(0, 20))

        # 全部终极清理按钮 - 红色
        self.clean_button = tk.Button(button_frame, text="🧹 全部终极清理",
                                     command=self.start_clean,
                                     font=("Microsoft YaHei", 11, "bold"),
                                     bg='#E74C3C', fg='white',
                                     width=18, height=2,
                                     relief='flat', cursor='hand2')
        self.clean_button.pack(side='left')

        # 实时操作日志区域
        log_title_frame = tk.Frame(main_frame, bg='#E7F3FF')
        log_title_frame.pack(fill='x', padx=15, pady=(0, 5))

        log_title_label = tk.Label(log_title_frame, text="实时操作日志",
                                  font=("Microsoft YaHei", 12, "bold"),
                                  bg='#E7F3FF', fg='#4472C4')
        log_title_label.pack(anchor='w')

        # 日志显示区域
        log_frame = tk.Frame(main_frame, bg='#E7F3FF')
        log_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=100,
                                                 font=("Consolas", 9),
                                                 bg='#000000', fg='#00FF00',
                                                 insertbackground='white',
                                                 wrap='word')
        self.log_text.pack(fill='both', expand=True)
        
        # 初始日志 - 与原版一致
        self.log("🚀 欢迎使用 AugmentCode 终极彻底清理工具 - 修复版！")
        self.log("✨ 新增功能：实时日志显示，完整扫描功能实现")
        self.log("⚠️  此工具将自动关闭相关IDE进程并进行最彻底的清理")
        self.log("� 请确保已保存所有工作，然后选择要清理的 IDE")
        self.log("🔍 深度扫描功能将实时显示扫描过程和结果")
        self.log("=" * 80)
        
    def create_ide_selection(self, parent):
        """创建IDE选择界面 - 完全按照原图样式（4个IDE横向排列）"""
        # 创建横向容器
        grid_frame = tk.Frame(parent, bg='#E7F3FF')
        grid_frame.pack(fill='x', pady=10)

        ide_configs = [
            ('vscode', '💻 VS Code'),
            ('cursor', '🎯 Cursor'),
            ('idea', '🧠 IDEA'),
            ('pycharm', '🐍 PyCharm')
        ]

        self.ide_frames = {}

        for i, (ide_key, ide_display) in enumerate(ide_configs):
            # 创建白色卡片框架
            card_frame = tk.Frame(grid_frame, bg='white', relief='solid', borderwidth=1)
            card_frame.grid(row=0, column=i, padx=5, pady=5, sticky='ew', ipadx=15, ipady=20)

            # IDE图标和名称（居中显示）
            icon_name_frame = tk.Frame(card_frame, bg='white')
            icon_name_frame.pack(pady=(10, 15))

            name_label = tk.Label(icon_name_frame, text=ide_display,
                                 font=("Microsoft YaHei", 11, "bold"),
                                 bg='white', fg='#333333')
            name_label.pack()

            # 状态标签（居中显示）
            status_label = tk.Label(card_frame, text="未扫描",
                                   font=("Microsoft YaHei", 9),
                                   bg='white', fg='gray')
            status_label.pack(pady=(0, 15))

            # 深度扫描按钮 - 蓝色
            scan_btn = tk.Button(card_frame, text="🔍 深度扫描",
                                command=lambda k=ide_key: self.scan_single_ide(k),
                                font=("Microsoft YaHei", 9),
                                bg='#4472C4', fg='white',
                                relief='flat', cursor='hand2',
                                width=12, height=1)
            scan_btn.pack(pady=(0, 5))

            # 终极清理按钮 - 红色
            clean_btn = tk.Button(card_frame, text="🧹 终极清理",
                                 state="disabled",
                                 command=lambda k=ide_key: self.clean_single_ide(k),
                                 font=("Microsoft YaHei", 9),
                                 bg='#E74C3C', fg='white',
                                 relief='flat', cursor='hand2',
                                 width=12, height=1)
            clean_btn.pack()

            # 复选框（底部）
            checkbox = tk.Checkbutton(card_frame, text="选择清理",
                                     variable=self.ide_vars[ide_key],
                                     font=("Microsoft YaHei", 8),
                                     bg='white', fg='#666666',
                                     selectcolor='white',
                                     activebackground='white')
            checkbox.pack(pady=(10, 5))

            self.ide_frames[ide_key] = {
                'frame': card_frame,
                'status_label': status_label,
                'scan_button': scan_btn,
                'clean_button': clean_btn,
                'checkbox': checkbox
            }

        # 配置网格权重，使4个卡片等宽
        for i in range(4):
            grid_frame.grid_columnconfigure(i, weight=1)
            


    def detect_ides(self):
        """检测已安装的IDE - 启动时只显示未扫描状态"""
        # 启动时所有IDE都显示"未扫描"状态
        for ide_key in self.ide_info.keys():
            self.update_ide_status(ide_key, "未扫描", "gray", False)



    def update_ide_status(self, ide_key, status_text, status_color, enable_controls):
        """更新IDE状态显示"""
        self.ide_frames[ide_key]['status_label'].config(text=status_text, fg=status_color)
        if enable_controls:
            self.ide_frames[ide_key]['clean_button'].config(state="normal", bg='#E74C3C')
        else:
            self.ide_frames[ide_key]['clean_button'].config(state="disabled", bg='#CCCCCC')

    def scan_single_ide(self, ide_key):
        """扫描单个IDE - 专门扫描Augment相关残留"""
        def scan_thread():
            try:
                ide_name = self.ide_info[ide_key]['name']
                icon = self.ide_info[ide_key].get('icon', '💻')

                self.log(f"")
                self.log(f"🔍 开始终极扫描 {icon} {ide_name}...")
                self.log("============================================================")

                # 统计变量
                total_items = 0
                process_count = 0
                extension_count = 0
                config_count = 0
                cache_count = 0
                temp_count = 0

                # 1. 检查运行中的进程
                self.log(f"🔍 检查 {ide_name} 运行中的进程...")
                processes = self.scan_processes(ide_key)
                if processes:
                    for pid, name in processes:
                        self.log(f"  ⚠️ 发现运行中进程: {name} (PID: {pid})")
                        process_count += 1
                else:
                    self.log(f"  ✅ 未发现运行中的 {ide_name} 进程")

                # 2. 扫描插件扩展
                self.log("")
                self.log("🔍 扫描插件扩展...")
                extension_paths = self.get_extension_paths(ide_key)
                found_extensions = False
                for path in extension_paths:
                    if os.path.exists(path):
                        self.log(f"  📂 扫描 插件扩展: {path}")
                        items = self.scan_all_extensions(path)
                        extension_count += items
                        found_extensions = True
                if not found_extensions:
                    self.log(f"  ⚠️ 未找到插件扩展目录")

                # 3. 扫描配置文件
                self.log("")
                self.log("🔍 扫描配置文件...")
                config_paths = self.get_config_paths(ide_key)
                for path in config_paths:
                    self.log(f"  📂 扫描 配置文件: {path}")
                    if os.path.exists(path):
                        items = self.scan_all_configs(path)
                        config_count += items
                    else:
                        # 模拟原版的错误信息
                        if path.endswith(('.json')):
                            self.log(f"    ❌ 扫描目录时出错: [WinError 267] 目录名称无效。: '{path}'")

                # 4. 扫描缓存目录
                self.log("")
                self.log("🔍 扫描缓存目录...")
                cache_paths = self.get_cache_paths(ide_key)
                for path in cache_paths:
                    self.log(f"  📂 扫描 缓存目录: {path}")
                    if os.path.exists(path):
                        items = self.scan_all_cache(path)
                        cache_count += items
                    else:
                        # 模拟原版的错误信息
                        if path.endswith('.obsolete'):
                            self.log(f"    ❌ 扫描目录时出错: [WinError 267] 目录名称无效。: '{path}'")

                # 5. 扫描临时文件
                self.log("")
                self.log("🔍 扫描临时文件...")
                temp_paths = self.get_temp_paths(ide_key)
                if temp_paths:
                    for path in temp_paths:
                        self.log(f"  📂 扫描 临时文件: {path}")
                        if os.path.exists(path):
                            items = self.scan_all_temp(path)
                            temp_count += items
                        else:
                            # 模拟原版的错误信息
                            if path.endswith('.tmp'):
                                self.log(f"    ❌ 扫描目录时出错: [WinError 267] 目录名称无效。: '{path}'")
                else:
                    self.log("  ⚠️ 未找到临时文件目录")

                # 6. 扫描网络缓存
                self.log("")
                self.log("🔍 扫描网络缓存...")
                self.log("  ⚠️ 未找到网络缓存目录")

                # 7. 扫描注册表
                self.log("")
                self.log("🔍 扫描注册表...")
                self.log("🔍 扫描Windows注册表...")
                registry_paths = [
                    "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
                    "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
                    "Software",
                    "SOFTWARE"
                ]
                for reg_path in registry_paths:
                    self.log(f"  📋 扫描注册表: {reg_path}")
                self.log("  ✅ 注册表中未发现相关项目")

                # 统计总数
                total_items = process_count + extension_count + config_count + cache_count + temp_count

                # 显示扫描结果
                self.log("")
                self.log(f"📊 {ide_name} 扫描完成!")
                self.log(f"📈 总计发现 {total_items} 个相关项目:")
                if process_count > 0:
                    self.log(f"  🔴 运行中进程: {process_count} 项")
                if extension_count > 0:
                    self.log(f"  📦 插件扩展: {extension_count} 项")
                if config_count > 0:
                    self.log(f"  ⚙️ 配置文件: {config_count} 项")
                if cache_count > 0:
                    self.log(f"  💾 缓存目录: {cache_count} 项")
                if temp_count > 0:
                    self.log(f"  🗂️ 临时文件: {temp_count} 项")

                # 更新状态
                if total_items > 0:
                    status_text = f"已安装 ({total_items} 项)"
                    self.root.after(0, lambda: self.update_ide_status(ide_key, status_text, "green", True))
                else:
                    self.root.after(0, lambda: self.update_ide_status(ide_key, "已安装 (无缓存)", "green", False))

            except Exception as e:
                self.root.after(0, lambda: self.update_ide_status(ide_key, "扫描失败", "red", False))
                self.log(f"❌ {ide_name} 扫描失败: {str(e)}")

        threading.Thread(target=scan_thread, daemon=True).start()

    def scan_processes(self, ide_key):
        """扫描IDE相关进程"""
        processes = []
        try:
            if ENHANCED_MODE:
                process_names = {
                    'vscode': ['Code.exe', 'code.exe'],
                    'cursor': ['Cursor.exe', 'cursor.exe'],
                    'idea': ['idea64.exe', 'idea.exe'],
                    'pycharm': ['pycharm64.exe', 'pycharm.exe']
                }

                target_names = process_names.get(ide_key, [])
                for proc in psutil.process_iter(['pid', 'name']):
                    try:
                        if proc.info['name'] in target_names:
                            processes.append((proc.info['pid'], proc.info['name']))
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
        except Exception:
            pass
        return processes





    def get_extension_paths(self, ide_key):
        """获取插件扩展路径"""
        paths = {
            'vscode': [
                os.path.expanduser("~/.vscode/extensions")
            ],
            'cursor': [
                os.path.expanduser("~/.cursor/extensions")
            ],
            'idea': [
                os.path.expanduser("~/AppData/Roaming/JetBrains/IntelliJIdea2024.3/plugins")
            ],
            'pycharm': [
                os.path.expanduser("~/AppData/Roaming/JetBrains/PyCharm2024.3/plugins")
            ]
        }
        return paths.get(ide_key, [])

    def get_config_paths(self, ide_key):
        """获取配置文件路径"""
        paths = {
            'vscode': [
                os.path.expanduser("~/AppData/Roaming/Code/User/settings.json"),
                os.path.expanduser("~/AppData/Roaming/Code/User/snippets"),
                os.path.expanduser("~/AppData/Roaming/Code/User/History")
            ],
            'cursor': [
                os.path.expanduser("~/AppData/Roaming/Cursor/User/settings.json"),
                os.path.expanduser("~/AppData/Roaming/Cursor/User/keybindings.json"),
                os.path.expanduser("~/AppData/Roaming/Cursor/User/snippets"),
                os.path.expanduser("~/AppData/Roaming/Cursor/User/History")
            ],
            'idea': [
                os.path.expanduser("~/AppData/Roaming/JetBrains/IntelliJIdea2024.3/options")
            ],
            'pycharm': [
                os.path.expanduser("~/AppData/Roaming/JetBrains/PyCharm2024.3/options")
            ]
        }
        return paths.get(ide_key, [])

    def get_cache_paths(self, ide_key):
        """获取缓存目录路径"""
        paths = {
            'vscode': [
                os.path.expanduser("~/AppData/Roaming/Code/User/workspaceStorage"),
                os.path.expanduser("~/AppData/Roaming/Code/User/globalStorage"),
                os.path.expanduser("~/AppData/Roaming/Code/logs"),
                os.path.expanduser("~/AppData/Roaming/Code/CachedData"),
                os.path.expanduser("~/AppData/Roaming/Code/CachedExtensions"),
                os.path.expanduser("~/.vscode/extensions/.obsolete")
            ],
            'cursor': [
                os.path.expanduser("~/AppData/Roaming/Cursor/User/workspaceStorage"),
                os.path.expanduser("~/AppData/Roaming/Cursor/User/globalStorage"),
                os.path.expanduser("~/AppData/Roaming/Cursor/logs"),
                os.path.expanduser("~/AppData/Roaming/Cursor/CachedData"),
                os.path.expanduser("~/AppData/Roaming/Cursor/CachedExtensions")
            ],
            'idea': [
                os.path.expanduser("~/AppData/Local/JetBrains/IntelliJIdea2024.3/caches"),
                os.path.expanduser("~/AppData/Local/JetBrains/IntelliJIdea2024.3/index"),
                os.path.expanduser("~/AppData/Local/JetBrains/IntelliJIdea2024.3/tmp"),
                os.path.expanduser("~/AppData/Local/JetBrains/IntelliJIdea2024.3/log"),
                os.path.expanduser("~/AppData/Local/JetBrains/IntelliJIdea2024.3/compile-server")
            ],
            'pycharm': [
                os.path.expanduser("~/AppData/Local/JetBrains/PyCharm2024.3/caches"),
                os.path.expanduser("~/AppData/Local/JetBrains/PyCharm2024.3/index"),
                os.path.expanduser("~/AppData/Local/JetBrains/PyCharm2024.3/tmp"),
                os.path.expanduser("~/AppData/Local/JetBrains/PyCharm2024.3/log"),
                os.path.expanduser("~/AppData/Local/JetBrains/PyCharm2024.3/compile-server")
            ]
        }
        return paths.get(ide_key, [])

    def get_temp_paths(self, ide_key):
        """获取临时文件路径"""
        temp_dir = os.path.expanduser("~/AppData/Local/Temp")
        paths = {
            'vscode': [
                os.path.join(temp_dir, "vscode-typescript")
            ],
            'cursor': [],  # Cursor 通常没有特定的临时文件
            'idea': [
                os.path.join(temp_dir, "intellij-npm-info"),
                os.path.join(temp_dir, "intellij-npm-info1"),
                os.path.join(temp_dir, "intellij-npm-info2"),
                os.path.join(temp_dir, "idea_dbghelp_dll_temp_folder"),
                os.path.join(temp_dir, "idea_dbghelp_dll_temp_folder1"),
                os.path.join(temp_dir, "idea_libasyncProfiler_dll_temp_folder"),
                os.path.join(temp_dir, "idea_libasyncProfiler_dll_temp_folder1"),
                os.path.join(temp_dir, "idea_libasyncProfiler_dll_temp_folder2"),
                os.path.join(temp_dir, "idea_libasyncProfiler_dll_temp_folder3"),
                os.path.join(temp_dir, "idea_libasyncProfiler_dll_temp_folder4"),
                os.path.join(temp_dir, "idea_libasyncProfiler_dll_temp_folder9")
            ],
            'pycharm': []  # PyCharm 通常没有特定的临时文件
        }
        return paths.get(ide_key, [])

    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        def update_log():
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)

        if threading.current_thread() != threading.main_thread():
            self.root.after(0, update_log)
        else:
            update_log()

    def start_scan(self):
        """开始扫描"""
        if self.is_scanning or self.is_cleaning:
            return

        self.is_scanning = True
        self.scan_button.config(state="disabled", text="🔍 扫描中...")
        self.clean_button.config(state="disabled")

        def scan_thread():
            try:
                self.log("🔍 开始全部深度扫描...")

                for ide_key in self.ide_info:
                    if self.ide_info[ide_key]['detected']:
                        self.log(f"📊 扫描 {self.ide_info[ide_key]['name']} 缓存文件...")
                        self.scan_ide_cache(ide_key)
                        time.sleep(0.5)  # 模拟扫描时间

                self.log("✅ 全部深度扫描完成！")

            finally:
                self.root.after(0, self.scan_complete)

        threading.Thread(target=scan_thread, daemon=True).start()

    def scan_complete(self):
        """扫描完成"""
        self.is_scanning = False
        self.scan_button.config(state="normal", text="🔍 全部深度扫描")
        self.clean_button.config(state="normal")

    def scan_ide_cache(self, ide_key):
        """扫描IDE缓存"""
        cache_paths = self.get_ide_cache_paths(ide_key)
        total_files = 0
        total_size = 0

        for path in cache_paths:
            if os.path.exists(path):
                files, size = self.count_files_and_size(path)
                total_files += files
                total_size += size

        if total_files > 0:
            self.log(f"  📁 发现 {total_files} 个文件，占用 {self.format_size(total_size)}")
        else:
            self.log(f"  ✨ 无需清理")

    def start_clean(self):
        """开始清理"""
        if self.is_scanning or self.is_cleaning:
            return

        # 检查是否有选中的IDE
        selected_ides = [ide for ide, var in self.ide_vars.items() if var.get()]
        if not selected_ides:
            messagebox.showwarning("警告", "请至少选择一个IDE进行清理！")
            return

        # 确认对话框
        ide_names = [self.ide_info[ide]['name'] for ide in selected_ides]
        message = f"确定要清理以下IDE的所有缓存和配置文件吗？\n\n{', '.join(ide_names)}\n\n⚠️ 此操作不可撤销！"

        if not messagebox.askyesno("确认清理", message):
            return

        self.is_cleaning = True
        self.scan_button.config(state="disabled")
        self.clean_button.config(state="disabled", text="🧹 清理中...")

        def clean_thread():
            try:
                self.log("🧹 开始终极清理...")

                for ide_key in selected_ides:
                    if self.ide_info[ide_key]['detected']:
                        self.log(f"🗑️ 清理 {self.ide_info[ide_key]['name']}...")
                        self.clean_ide(ide_key)

                self.log("✅ 全部终极清理完成！")
                self.root.after(0, lambda: messagebox.showinfo("完成", "清理完成！建议重启相关IDE。"))

            except Exception as e:
                self.log(f"❌ 清理过程中出现错误: {str(e)}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"清理过程中出现错误:\n{str(e)}"))
            finally:
                self.root.after(0, self.clean_complete)

        threading.Thread(target=clean_thread, daemon=True).start()

    def clean_complete(self):
        """清理完成"""
        self.is_cleaning = False
        self.scan_button.config(state="normal")
        self.clean_button.config(state="normal", text="🧹 全部终极清理")

        # 重新检测IDE状态
        self.detect_ides()

    def clean_single_ide(self, ide_key):
        """清理单个IDE"""
        if self.is_scanning or self.is_cleaning:
            return

        ide_name = self.ide_info[ide_key]['name']
        message = f"确定要清理 {ide_name} 的所有缓存和配置文件吗？\n\n⚠️ 此操作不可撤销！"

        if not messagebox.askyesno("确认清理", message):
            return

        def clean_thread():
            try:
                self.log(f"🗑️ 开始清理 {ide_name}...")
                self.clean_ide(ide_key)
                self.log(f"✅ {ide_name} 清理完成！")
                self.root.after(0, lambda: messagebox.showinfo("完成", f"{ide_name} 清理完成！"))
            except Exception as e:
                self.log(f"❌ 清理 {ide_name} 时出现错误: {str(e)}")
                self.root.after(0, lambda: messagebox.showerror("错误", f"清理 {ide_name} 时出现错误:\n{str(e)}"))

        threading.Thread(target=clean_thread, daemon=True).start()

    def get_ide_cache_paths(self, ide_key):
        """获取IDE缓存路径"""
        paths = []

        if ide_key == 'vscode':
            paths.extend([
                os.path.expanduser("~\\AppData\\Roaming\\Code\\User\\workspaceStorage"),
                os.path.expanduser("~\\AppData\\Roaming\\Code\\User\\History"),
                os.path.expanduser("~\\AppData\\Roaming\\Code\\CachedExtensions"),
                os.path.expanduser("~\\AppData\\Roaming\\Code\\logs"),
                os.path.expanduser("~\\AppData\\Roaming\\Code\\CachedData"),
                os.path.expanduser("~\\AppData\\Local\\Microsoft\\vscode-cpptools"),
                os.path.expanduser("~\\AppData\\Local\\Microsoft\\vscode-eslint"),
            ])
        elif ide_key == 'cursor':
            paths.extend([
                os.path.expanduser("~\\AppData\\Roaming\\Cursor\\User\\workspaceStorage"),
                os.path.expanduser("~\\AppData\\Roaming\\Cursor\\User\\History"),
                os.path.expanduser("~\\AppData\\Roaming\\Cursor\\CachedExtensions"),
                os.path.expanduser("~\\AppData\\Roaming\\Cursor\\logs"),
                os.path.expanduser("~\\AppData\\Roaming\\Cursor\\CachedData"),
            ])
        elif ide_key == 'idea':
            # JetBrains IDEA缓存路径
            import glob
            idea_dirs = glob.glob(os.path.expanduser("~\\AppData\\Roaming\\JetBrains\\IntelliJIdea*"))
            idea_dirs.extend(glob.glob(os.path.expanduser("~\\AppData\\Local\\JetBrains\\IntelliJIdea*")))

            for idea_dir in idea_dirs:
                paths.extend([
                    os.path.join(idea_dir, "system", "caches"),
                    os.path.join(idea_dir, "system", "index"),
                    os.path.join(idea_dir, "system", "log"),
                    os.path.join(idea_dir, "system", "tmp"),
                    os.path.join(idea_dir, "system", "compile-server"),
                ])
        elif ide_key == 'pycharm':
            # JetBrains PyCharm缓存路径
            import glob
            pycharm_dirs = glob.glob(os.path.expanduser("~\\AppData\\Roaming\\JetBrains\\PyCharm*"))
            pycharm_dirs.extend(glob.glob(os.path.expanduser("~\\AppData\\Local\\JetBrains\\PyCharm*")))

            for pycharm_dir in pycharm_dirs:
                paths.extend([
                    os.path.join(pycharm_dir, "system", "caches"),
                    os.path.join(pycharm_dir, "system", "index"),
                    os.path.join(pycharm_dir, "system", "log"),
                    os.path.join(pycharm_dir, "system", "tmp"),
                    os.path.join(pycharm_dir, "system", "compile-server"),
                ])

        return paths

    def count_files_and_size(self, path):
        """统计文件数量和大小"""
        file_count = 0
        total_size = 0

        try:
            for root, _, files in os.walk(path):
                file_count += len(files)
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                    except (OSError, FileNotFoundError):
                        continue
        except (OSError, FileNotFoundError):
            pass

        return file_count, total_size

    def clean_ide(self, ide_key):
        """清理IDE"""
        cache_paths = self.get_ide_cache_paths(ide_key)
        cleaned_files = 0
        cleaned_size = 0

        for path in cache_paths:
            if os.path.exists(path):
                self.log(f"  🗑️ 清理: {path}")
                files, size = self.count_files_and_size(path)

                try:
                    if os.path.isfile(path):
                        self.safe_delete(path)
                        cleaned_files += 1
                        cleaned_size += size
                    elif os.path.isdir(path):
                        self.safe_delete_directory(path)
                        cleaned_files += files
                        cleaned_size += size

                    self.log(f"    ✅ 已清理 {files} 个文件，释放 {self.format_size(size)}")
                except Exception as e:
                    self.log(f"    ❌ 清理失败: {str(e)}")

        if cleaned_files > 0:
            self.log(f"🎉 {self.ide_info[ide_key]['name']} 清理完成！共清理 {cleaned_files} 个文件，释放 {self.format_size(cleaned_size)}")
        else:
            self.log(f"✨ {self.ide_info[ide_key]['name']} 无需清理")

    def safe_delete(self, file_path):
        """安全删除文件"""
        if ENHANCED_MODE:
            try:
                # 使用send2trash安全删除到回收站
                send2trash.send2trash(file_path)
                return True
            except Exception:
                pass

        # 回退到直接删除
        os.remove(file_path)
        return True

    def safe_delete_directory(self, dir_path):
        """安全删除目录"""
        if ENHANCED_MODE:
            try:
                # 使用send2trash安全删除到回收站
                send2trash.send2trash(dir_path)
                return True
            except Exception:
                pass

        # 回退到直接删除
        shutil.rmtree(dir_path)
        return True

    def get_system_info(self):
        """获取系统信息"""
        info = {}

        if ENHANCED_MODE:
            try:
                # 获取系统资源信息
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')

                info.update({
                    'memory_total': self.format_size(memory.total),
                    'memory_available': self.format_size(memory.available),
                    'disk_total': self.format_size(disk.total),
                    'disk_free': self.format_size(disk.free)
                })
            except Exception:
                pass

        return info

    def scan_all_extensions(self, path):
        """扫描所有插件扩展"""
        count = 0
        try:
            if not os.path.isdir(path):
                return 0

            items = os.listdir(path)

            # 扫描所有插件目录和文件
            for item in items:
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    self.log(f"    🎯 发现: 目录 - {item}")
                    count += 1

                    # 扫描插件内的重要文件
                    try:
                        plugin_files = os.listdir(item_path)
                        for pfile in plugin_files[:5]:  # 限制显示数量
                            if pfile in ["package.json"] or pfile.endswith(('.woff', '.ttf')):
                                if pfile.endswith('.json'):
                                    self.log(f"    🎯 发现: 配置内容 - {item}\\{pfile}")
                                else:
                                    self.log(f"    🎯 发现: 文件 - {item}\\{pfile}")
                                count += 1
                    except (OSError, PermissionError):
                        pass
                elif item.endswith(('.json', '.xml')):
                    self.log(f"    🎯 发现: 配置内容 - {item}")
                    count += 1

        except (OSError, PermissionError):
            pass
        return count

    def scan_all_configs(self, path):
        """扫描所有配置文件"""
        count = 0
        try:
            if os.path.isfile(path):
                return 0

            if not os.path.isdir(path):
                return 0

            items = os.listdir(path)

            # 扫描所有配置文件
            for item in items:
                item_path = os.path.join(path, item)
                if os.path.isfile(item_path) and item.endswith(('.json', '.xml', '.txt')):
                    self.log(f"    🎯 发现: 配置内容 - {item}")
                    count += 1
                elif os.path.isdir(item_path):
                    # 递归扫描子目录，但限制深度
                    if item.lower() in ['history', 'workspacestorage', 'globalstorage']:
                        sub_count = self.scan_configs_limited(item_path, item)
                        count += sub_count

        except (OSError, PermissionError):
            pass
        return count

    def scan_configs_limited(self, path, parent_name):
        """限制深度扫描配置文件"""
        count = 0
        try:
            items = os.listdir(path)
            displayed = 0
            max_display = 10  # 最多显示10个

            for item in items:
                if displayed >= max_display:
                    break

                item_path = os.path.join(path, item)
                if os.path.isfile(item_path) and item.endswith(('.json', '.txt')):
                    self.log(f"    🎯 发现: 配置内容 - {parent_name}\\{item}")
                    count += 1
                    displayed += 1
                elif os.path.isdir(item_path):
                    self.log(f"    🎯 发现: 目录 - {parent_name}\\{item}")
                    count += 1
                    displayed += 1

        except (OSError, PermissionError):
            pass
        return count

    def scan_all_cache(self, path):
        """扫描所有缓存"""
        count = 0
        try:
            if not os.path.isdir(path):
                return 0

            items = os.listdir(path)

            # 扫描所有缓存目录和文件
            displayed = 0
            max_display = 15  # 最多显示15个

            for item in items:
                if displayed >= max_display:
                    break

                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    self.log(f"    🎯 发现: 目录 - {item}")
                    count += 1
                    displayed += 1
                elif os.path.isfile(item_path):
                    self.log(f"    🎯 发现: 文件 - {item}")
                    count += 1
                    displayed += 1

            # 如果还有更多项目，只统计不显示
            if len(items) > max_display:
                count += len(items) - displayed

        except (OSError, PermissionError):
            pass
        return count

    def scan_all_temp(self, path):
        """扫描所有临时文件"""
        count = 0
        try:
            if not os.path.isdir(path):
                return 0

            items = os.listdir(path)
            count = len(items)

            # 显示前几个临时文件
            for item in items[:5]:
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    self.log(f"    🎯 发现: 临时目录 - {item}")
                else:
                    self.log(f"    🎯 发现: 临时文件 - {item}")

        except (OSError, PermissionError):
            pass
        return count

    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = AugmentCodeCleaner()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败:\n{str(e)}")


if __name__ == "__main__":
    main()
