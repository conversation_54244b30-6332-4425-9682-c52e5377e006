#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IDE缓存清理工具 - 启动脚本
"""

import sys
import os
import subprocess

def check_python():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        print(f"💡 当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = ['psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package} 未安装")
    
    if missing_packages:
        print(f"⚠️ 正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            for package in missing_packages:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"  ✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"  ❌ 安装失败: {e}")
            print("  💡 请手动运行: pip install psutil")
            return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("  IDE缓存清理工具")
    print("=" * 60)
    print()
    
    # 检查Python版本
    if not check_python():
        input("按回车键退出...")
        return False
    
    print()
    
    # 安装依赖
    if not install_dependencies():
        input("按回车键退出...")
        return False
    
    print()
    
    # 检查主程序文件
    if not os.path.exists("augmentcode_cleaner.py"):
        print("❌ 错误: 未找到主程序文件 augmentcode_cleaner.py")
        input("按回车键退出...")
        return False
    
    print("✅ 主程序文件存在")
    print()
    print("🚀 启动IDE缓存清理工具...")
    print()
    
    # 启动主程序
    try:
        from augmentcode_cleaner import main as cleaner_main
        cleaner_main()
        return True
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        input("按回车键退出...")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
