# IDE缓存清理工具

清理各种IDE的缓存、配置文件和临时文件

## 🎯 功能特点

- **全面清理**: 清理所有缓存、配置文件和临时文件
- **多IDE支持**: 支持 VS Code、Cursor、IDEA、PyCharm
- **安全清理**: 智能识别清理路径，避免误删重要文件
- **直观界面**: 图形化界面，操作简单
- **实时日志**: 显示详细的扫描和清理过程

## 📋 支持的IDE

| IDE | 图标 | 清理内容 |
|-----|------|----------|
| VS Code | 💻 | 工作区存储、全局存储、日志、缓存数据、扩展缓存 |
| Cursor | 🎯 | 工作区存储、全局存储、日志、缓存数据、扩展缓存 |
| IDEA | 🧠 | 系统缓存、索引文件、临时文件、日志、编译服务器缓存 |
| PyCharm | 🐍 | 系统缓存、索引文件、临时文件、日志、编译服务器缓存 |

## 🚀 使用方法

### 启动程序
```bash
# 推荐方式：使用启动脚本
python run.py

# 或直接运行主程序
python augmentcode_cleaner.py
```

## 📦 依赖要求

- Python 3.6+
- tkinter (通常随Python安装)
- psutil (自动安装)

## 🔍 清理内容

### VS Code
- `~/AppData/Roaming/Code/User/workspaceStorage` - 工作区存储
- `~/AppData/Roaming/Code/User/globalStorage` - 全局存储
- `~/AppData/Roaming/Code/logs` - 日志文件
- `~/AppData/Roaming/Code/CachedData` - 缓存数据
- `~/AppData/Roaming/Code/CachedExtensions` - 扩展缓存
- `~/.vscode/extensions/.obsolete` - 过期扩展

### Cursor  
- `~/AppData/Roaming/Cursor/User/workspaceStorage` - 工作区存储
- `~/AppData/Roaming/Cursor/User/globalStorage` - 全局存储
- `~/AppData/Roaming/Cursor/logs` - 日志文件
- `~/AppData/Roaming/Cursor/CachedData` - 缓存数据
- `~/AppData/Roaming/Cursor/CachedExtensions` - 扩展缓存

### IDEA
- `~/AppData/Local/JetBrains/IntelliJIdea*/caches` - 系统缓存
- `~/AppData/Local/JetBrains/IntelliJIdea*/index` - 索引文件
- `~/AppData/Local/JetBrains/IntelliJIdea*/tmp` - 临时文件
- `~/AppData/Local/JetBrains/IntelliJIdea*/log` - 日志文件
- `~/AppData/Local/JetBrains/IntelliJIdea*/compile-server` - 编译服务器缓存

### PyCharm
- `~/AppData/Local/JetBrains/PyCharm*/caches` - 系统缓存
- `~/AppData/Local/JetBrains/PyCharm*/index` - 索引文件
- `~/AppData/Local/JetBrains/PyCharm*/tmp` - 临时文件
- `~/AppData/Local/JetBrains/PyCharm*/log` - 日志文件
- `~/AppData/Local/JetBrains/PyCharm*/compile-server` - 编译服务器缓存

## 🛡️ 安全说明

- 只清理缓存和临时文件，不会删除用户代码
- 清理前会显示详细的扫描结果
- 支持进程检测，避免在IDE运行时清理
- 所有清理路径都经过精心设计，避免误删重要文件

## 📝 使用步骤

1. **启动工具**: 运行 `python run.py`
2. **深度扫描**: 点击任意IDE的"🔍 深度扫描"按钮
3. **查看结果**: 在日志区域查看扫描到的文件
4. **执行清理**: 点击"🗑️ 终极清理"按钮删除文件
5. **确认结果**: 查看清理日志确认操作完成

## 🔧 界面说明

- **IDE卡片**: 显示每个IDE的状态和操作按钮
- **深度扫描**: 扫描并显示所有缓存和配置文件
- **终极清理**: 删除扫描到的文件
- **日志区域**: 显示详细的操作过程和结果

## ⚠️ 注意事项

- 清理前请确保相关IDE已关闭
- 建议先使用"深度扫描"查看要清理的文件
- 清理操作不可撤销，请谨慎操作
- 如有重要数据，请提前备份

## 🐛 故障排除

### 问题1: Python未安装
```
解决方案: 下载并安装Python 3.6+
下载地址: https://www.python.org/downloads/
```

### 问题2: 依赖包安装失败
```bash
# 手动安装
pip install psutil
```

### 问题3: 权限不足
```
解决方案: 以管理员身份运行程序
```

## 📄 文件说明

- `augmentcode_cleaner.py` - 主程序文件
- `run.py` - Python启动脚本
- `README.md` - 使用说明文档

## 🎉 完成

现在您有了一个全面的IDE缓存清理工具，可以清理所有缓存、配置文件和临时文件！
